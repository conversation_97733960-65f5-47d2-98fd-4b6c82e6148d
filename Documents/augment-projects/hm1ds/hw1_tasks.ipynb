{"cells": [{"cell_type": "markdown", "id": "9c4bb33b", "metadata": {}, "source": ["\n", "# Homework 1: Practical Introduction to NumPy\n", "\n", "**Course:** 4780/6780 Fundamentals of Data Science  \n", "**Instructor:** <PERSON><PERSON>  \n", "**Due:** 11pm, Wednesday, September 24, 2025\n", "\n", "---\n", "\n", "## How to use this notebook\n", "- Work top-to-bottom. Run each cell after you edit it.\n", "- Keep your answers clear and short. Use Markdown for reasoning.\n", "- Do not delete the problem statements.\n", "- You may add cells as needed.\n", "- When finished, run *Kernel → Restart & Run All*, verify outputs, then submit.\n", "\n", "**Honor code:** Write your own code. You may discuss high-level ideas with peers, but all implementation must be yours.\n"]}, {"cell_type": "code", "execution_count": 1, "id": "1e076207", "metadata": {}, "outputs": [], "source": ["import numpy as np"]}, {"cell_type": "markdown", "id": "bfea629a", "metadata": {}, "source": ["\n", "---\n", "## Part 1. Basic Array Manipulation (20 pts)\n", "\n", "1. Create a 1‑D NumPy array with integers from **1 to 20**.  \n", "   - Reshape it into a **4×5** matrix.  \n", "   - Extract:\n", "     - the **second column**,\n", "     - the **last row**,\n", "     - a **2×2 sub‑matrix** from the middle (clearly state indices used).\n", "\n", "2. Compute the **row-wise** and **column-wise** sums. Verify them against `np.sum` with the `axis` argument.\n"]}, {"cell_type": "code", "execution_count": null, "id": "b97fbf32", "metadata": {}, "outputs": [], "source": ["\n", "# === Your code for Part 1 === \n", "# Edit as needed, but keep variable names! \n", "\n", "# 1) build the array and reshape (5 pts)\n", "arr1d = ...\n", "A = ...\n", "\n", "# 2) extracts (5 pts)\n", "second_col = ...\n", "last_row = ...\n", "middle_2x2 = ...   \n", "\n", "# 3) reductions (5 pts)\n", "row_sums = ...\n", "col_sums = ...\n", "\n", "\n", "\n", "# ------------------------------\n", "#   ↓Checks (do NOT remove!)↓\n", "# ------------------------------\n", "assert A.shape == (4, 5)\n", "assert second_col.shape == (4,)\n", "assert last_row.shape == (5,)\n", "assert middle_2x2.shape == (2, 2)\n", "\n", "print(\"A =\\n\", A)\n", "print(\"\\nsecond_col =\", second_col)\n", "print(\"\\nlast_row   =\", last_row)\n", "print(\"\\nmiddle_2x2 =\\n\", middle_2x2)\n", "print(\"\\nrow_sums   =\", row_sums)\n", "print(\"col_sums   =\", col_sums)\n"]}, {"cell_type": "markdown", "id": "0dcd820e", "metadata": {}, "source": ["\n", "**Explain briefly: (5 pts)** How did you choose the slice for the 2×2 sub‑matrix? Why do `axis=0` and `axis=1` correspond to columns and rows respectively?\n"]}, {"cell_type": "markdown", "id": "ebe238d7", "metadata": {}, "source": ["*Write your answer here.*"]}, {"cell_type": "markdown", "id": "620b1a72", "metadata": {}, "source": ["\n", "---\n", "## Part 2. Useful Array Manipulations (25 pts)\n", "\n", "In the following questions, you must get the shapes of the output correct for your answer to be accepted. The number and placement of brackets need to match!"]}, {"cell_type": "markdown", "id": "89dea37a", "metadata": {}, "source": ["**2.1.** Write a function that takes a list of numbers and returns a 2D NumPy array in the form of a **row vector**.  \n", "By convention, a row vector has shape `(1, d)`, where `d` is the number of elements in the input list."]}, {"cell_type": "code", "execution_count": null, "id": "d60a350f", "metadata": {}, "outputs": [], "source": ["# Edit as needed, but keep variable/function names! \n", "def row_vector(value_list):\n", "    ...\n", "\n", "\n", "# ------------------------------\n", "#   ↓Checks (do NOT remove!)↓\n", "# ------------------------------\n", "v = row_vector([1, 2, 3, 4])\n", "print(v)           # [[1 2 3 4]]\n", "print(v.shape)     # (1, 4)"]}, {"cell_type": "markdown", "id": "b9c1abfd", "metadata": {}, "source": ["**2.2.** Write a function that takes a list of numbers and returns a 2D NumPy array representing a **column vector** containing those numbers.  \n", "By convention, a column vector has shape `(d, 1)`, where `d` is the number of elements.  \n", "You may use your `row_vector` function."]}, {"cell_type": "code", "execution_count": null, "id": "57b12cc4", "metadata": {}, "outputs": [], "source": ["# Edit as needed, but keep variable/function names! \n", "def column_vector(value_list):\n", "    ...\n", "\n", "\n", "# ------------------------------\n", "#   ↓Checks (do NOT remove!)↓\n", "# ------------------------------\n", "cv = column_vector([1, 2, 3, 4])\n", "print(cv)\n", "print(cv.shape)   # (4, 1)"]}, {"cell_type": "markdown", "id": "9d7fd743", "metadata": {}, "source": ["**2.3** Write a function that takes a **column vector** and returns its **Euclidean length** (magnitude) as a scalar (single number).  \n", "You may **not** use `np.linalg.norm` and you may **not** use loops.\n", "\n", "The Euclidean length of a vector $v$ is defined as:\n", "\n", "$$\n", "\\|v\\| = \\sqrt{ \\sum_{i=1}^{d} v_i^2 }\n", "$$"]}, {"cell_type": "code", "execution_count": null, "id": "67c06bfc", "metadata": {}, "outputs": [], "source": ["# Edit as needed, but keep variable/function names! \n", "def vector_length(col_v):\n", "    ...\n", "\n", "# ------------------------------\n", "#   ↓Checks (do NOT remove!)↓\n", "# ------------------------------\n", "v = np.array([3,4,12]).reshape(-1,1)\n", "print(v)\n", "print(vector_length(v))  # 13.0"]}, {"cell_type": "markdown", "id": "d078b178", "metadata": {}, "source": ["**2.4.** Write a function that takes a **column vector** and returns a **unit vector** (a vector of length 1) in the same direction.  \n", "You may assume the input is not the zero vector and has dimension ≥ 1.  \n", "You may **not** use loops.  \n", "Use your previously defined `vector_length` function (do not redefine it)."]}, {"cell_type": "code", "execution_count": null, "id": "a736e642", "metadata": {}, "outputs": [], "source": ["# Edit as needed, but keep variable/function names! \n", "def unit_vector(col_v):\n", "    ...\n", "\n", "# ------------------------------\n", "#   ↓Checks (do NOT remove!)↓\n", "# ------------------------------\n", "v = np.array([[3], [4]])\n", "u = unit_vector(v)\n", "print(u)\n", "print(vector_length(u)) "]}, {"cell_type": "markdown", "id": "cf650e20", "metadata": {}, "source": ["**2.5.** Write a function that takes a **2D NumPy array** and returns its **final column** as a 2D array (shape `(n, 1)`).  \n", "You may **not** use loops.  \n", "*Hint:* negative indices can be used to count from the end of the array."]}, {"cell_type": "code", "execution_count": null, "id": "45c74d08", "metadata": {}, "outputs": [], "source": ["# Edit as needed, but keep variable/function names! \n", "def final_column(arr):\n", "    ... \n", "\n", "# ------------------------------\n", "#   ↓Checks (do NOT remove!)↓\n", "# ------------------------------\n", "arr = np.arange(1, 13).reshape(3, 4)\n", "print(arr)\n", "print(final_column(arr))        \n", "print(final_column(arr).shape)  "]}, {"cell_type": "markdown", "id": "cbf17798", "metadata": {}, "source": ["\n", "---\n", "## Part 3. Practical Simulation (25 pts)\n", "\n", "A small company wants to simulate customer spending (in dollars). Assume spending is **normally distributed** with mean **55** and standard deviation **15**.\n", "\n", "1. Simulate the spending of **1,000** customers using `rng.normal` (use the provided `rng` for reproducibility).\n", "2. <PERSON><PERSON><PERSON>:\n", "   - **average** simulated spending,\n", "   - **count** of customers who spent **more than $100**,\n", "   - **90th percentile** spending (use `np.percentile`).\n", "3. In 3–5 lines, comment on whether a normal assumption seems reasonable in retail spending, and when it might fail.\n", "\n", "> **Deliverables:** Numeric answers and the brief discussion.\n"]}, {"cell_type": "code", "execution_count": null, "id": "c55de23b", "metadata": {}, "outputs": [], "source": ["\n", "# Reproducibility: set a seed when simulating (do NOT remove!)\n", "SEED = 42\n", "rng = np.random.default_rng(SEED)\n", "\n", "# Edit as needed, but keep variable/function names! \n", "\n", "\n", "# 1) generate synthetic spending data (5 pts)\n", "spending = ...\n", "\n", "# 2) compute average spending (5 pts)\n", "avg_spending = ...\n", "\n", "# 3) count how many values are greater than 100 (5 pts)\n", "count_over_100 = ...\n", "\n", "# 4) compute the 90th percentile (5 pts)\n", "p90 = ...\n", "\n", "\n", "# ------------------------------\n", "#   ↓Checks (do NOT remove!)↓\n", "# ------------------------------\n", "print(f\"Average spending: ${avg_spending:.2f}\")\n", "print(f\"Customers > $100: {count_over_100}\")\n", "print(f\"90th percentile:  ${p90:.2f}\")\n"]}, {"cell_type": "markdown", "id": "613b2ce5", "metadata": {}, "source": ["**Discussion (3–5 sentences): (5pts)**  \n", "\n", "Is a normal distribution an appropriate model for spending data? What are its key limitations, particularly regarding non-negativity and the presence of extreme values?  \n", "\n", "If the normal model is not ideal, what alternative distributions or modeling approaches (e.g., log-normal, gamma, Pareto, mixture models) might better capture the skewness and heavy-tailed nature of spending?  \n", "\n", "You are encouraged to consult outside resources to support and enrich your discussion."]}, {"cell_type": "markdown", "id": "c514e826", "metadata": {}, "source": ["*Write your answer here.*"]}, {"cell_type": "markdown", "id": "cfd71bd9", "metadata": {}, "source": ["\n", "---\n", "## Part 4. <PERSON><PERSON> (30 pts)\n", "\n", "A lottery game picks **6 distinct numbers** from **1 to 49**.  \n", "- Use NumPy to simulate $N=4400$ random tickets.  \n", "- Estimate the probability that **at least two** players pick **exactly the same** ticket.\n", "\n", " To do this, you need to \n", "1) Create function `sample_tickets(N, universe=49, k=6, rng=None) -> (N, k) int array` \n", "to simulate N tickets (make it vectorized, do NOT use Python loops).\n", "\n", "    > Your first move may be something like `np.array([np.sort(rng.choice(49, 6, replace=False)) + 1 for _ in range(N)])`.\n", "    > However, this version uses for loop and is slow. Instead, use the following vectorized version:  \n", "    > 1) Generate a matrix of size `(N, 49)` of uniform random numbers in `[0,1)`.  \n", "    > 2) Take the 6 smallest indices → a sample **without replacement** in each row.  \n", "    >     - A trick is to use `np.argpartition()` which finds the k smallest indices without full sorting.  \n", "    > 3) Sort each ticket's 6 numbers so that identical tickets compare equal.  \n", "\n", "2) Create function `has_collision(tickets: (N, k) array) -> bool` to detect duplicates.\n", "    - You may use `np.unique()` for this.\n", "\n", "3) Create function  `estimate_collision_prob(N=4400, trials=100, rng=None) -> float` to estimate probability.\n", "    - Repeat steps (1)–(2) for 100 trials and average the 0/1 collision indicator."]}, {"cell_type": "markdown", "id": "f175445d", "metadata": {}, "source": ["\n", "#### Quick guide on `np.argpartition()`\n", "\n", "`np.argpartition(a, kth, axis=...)`  \n", "- Performs a **partial sort**: places the element at position `kth` in its final sorted location.  \n", "- All elements **before** index `kth` are smaller (order arbitrary), all elements **after** are larger.  \n", "- Much faster than full sorting when you only need the smallest (or largest) `k` elements.\n", "\n", "\n", "Let say we want to find 3 smallest elements. Then:\n", "\n", "**For 1D**\n", "\n", "```python\n", "arr = np.array([30, 10, 50, 20, 40])\n", "idx = np.argpartition(arr, 2)[:3]\n", "``` \n", "\n", "- `np.argpartition(arr, 2)` could be `[1, 3, 0, 4, 2]`  \n", "- The first 3 indices 1, 3, 0 point to the 3 smallest elements 10, 20, 30 (unsorted).  \n", "\n", "\n", "**For 2D**\n", "\n", "`keys = np.random.rand(4, 6)`      \n", "`idx = np.argpartition(keys, 2, axis=1)[:,:3]`  \n", "\n", "- For each row, `idx` gives the indices of the 3 smallest elements.  "]}, {"cell_type": "code", "execution_count": null, "id": "03230ae3", "metadata": {}, "outputs": [], "source": ["rng = np.random.default_rng(42)\n", "\n", "# Edit as needed, but keep variable/function names! \n", "\n", "def sample_tickets(N, universe=49, k=6):\n", "    ...\n", "\n", "def has_collision(tickets):\n", "    ...\n", "\n", "def estimate_collision_prob(N, trials):\n", "    ...\n", "\n", "\n", "# ------------------------------\n", "#   ↓Checks (do NOT remove!)↓\n", "# ------------------------------\n", "N = 4400\n", "trials = 100\n", "print(f\"Estimated P(collision) for N={N} over {trials} trials: {estimate_collision_prob(N, trials):.2f}\")"]}, {"cell_type": "markdown", "id": "6d0a6198", "metadata": {}, "source": ["\n", "---\n", "## Submission Checklist\n", "\n", "- All code cells **run without errors** from a clean kernel.\n", "- Brief markdown reasoning included where requested.\n", "- File name: `hw1_<LastName>_<FirstName>.ipynb`.\n", "- Submission is not zipped.\n", "- You submited to iCollege before the deadline.\n", "\n", "## Grading guidance (100 pts total):\n", "- Part 1 (20 pts): 3 tasks × 5 pts each + explanation (5 pts)  \n", "- Part 2 (25 pts): 5 tasks × 5 pts each  \n", "- Part 3 (25 pts): 4 tasks × 5 pts each + explanation (5 pts)  \n", "- Part 4 (30 pts): 3 functions × 10 pts each  \n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.5"}}, "nbformat": 4, "nbformat_minor": 5}